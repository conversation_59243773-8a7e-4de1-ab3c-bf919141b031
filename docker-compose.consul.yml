version: '3.8'

networks:
  microservices:
    driver: bridge

services:
  # ==================== Consul 服务发现 ====================
  consul:
    image: consul:1.15
    container_name: consul-server
    hostname: consul
    command: >
      consul agent -server -bootstrap-expect=1 -datacenter=dc1 -data-dir=/consul/data
      -bind=0.0.0.0 -client=0.0.0.0 -retry-join=consul -ui
    ports:
      - "8500:8500"
      - "8600:8600/udp"
    volumes:
      - consul_data:/consul/data
    networks:
      - microservices
    healthcheck:
      test: ["CMD", "consul", "members"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    restart: unless-stopped

  # 注意: PostgreSQL 和 Redis 使用外部服务
  # 外部数据库: ************:5432/lens
  # 外部Redis: ************:6379/5

  # ==================== 产品相似度微服务 ====================
  product-similarity:
    image: product-similarity:latest
    container_name: product-similarity-server
    hostname: product-similarity
    depends_on:
      consul:
        condition: service_healthy
    environment:
      # Consul配置
      - CONSUL_HOST=consul
      - CONSUL_PORT=8500

      # 服务配置
      - SERVICE_NAME=product-similarity
      - SERVICE_PORT=8000
      - SERVICE_TAGS=api,product,similarity
      - ENVIRONMENT=production

      # 服务器配置
      - HOST=0.0.0.0
      - PORT=8000
      - LOG_LEVEL=INFO

      # 数据库配置 (使用外部数据库)
      - PG_HOST=************
      - PG_PORT=5432
      - PG_USER=lens
      - PG_PASSWORD=Ls.3956573
      - PG_DB=lens

      # Redis配置 (使用外部Redis)
      - REDIS_HOST=************
      - REDIS_PORT=6379
      - REDIS_PASSWORD=ls3956573
      - REDIS_DB=5

      # AI配置
      - TEXT_ENDPOINTS=[{"url":"http://************:3000","api_key":"sk-xeqWxvtLPPLtxfxFrcWxmT9MjsUHB40KXyGBWwialVn99ogK","model":"deepseek/deepseek-chat-v3-0324:free","is_multimodal":false},{"url":"http://************:3000","api_key":"sk-xeqWxvtLPPLtxfxFrcWxmT9MjsUHB40KXyGBWwialVn99ogK","model":"deepseek-ai/DeepSeek-V3","is_multimodal":false}]

      # 图片描述服务配置
      - IMAGE_DESCRIPTION_API_URL=http://localhost:8001/describe-image

      # 业务配置
      - MAX_CONCURRENT_REQUESTS=10
      - REQUEST_TIMEOUT=30
      - CACHE_TTL=3600
      - AI_TIMEOUT=120
      - AI_TEMPERATURE=0.1

    ports:
      - "8000:8000"
    networks:
      - microservices
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8000/health"]
      interval: 15s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped

  # Nginx网关已禁用，直接访问微服务端口8000

volumes:
  consul_data:
    driver: local

# ==================== 使用说明 ====================
#
# 1. 启动所有服务：
#    docker-compose -f docker-compose.consul.yml up -d
#
# 2. 查看服务状态：
#    docker-compose -f docker-compose.consul.yml ps
#
# 3. 查看日志：
#    docker-compose -f docker-compose.consul.yml logs -f product-similarity
#
# 4. 停止所有服务：
#    docker-compose -f docker-compose.consul.yml down
#
# 5. 重新构建并启动：
#    docker-compose -f docker-compose.consul.yml up --build -d
#
# 6. 访问服务：
#    - Consul UI: http://localhost:8500
#    - API文档: http://localhost/docs (通过网关)
#    - 健康检查: http://localhost/api/product-similarity/health
#
# 7. 环境变量配置：
#    - 复制 .env.example 到 .env
#    - 修改 .env 中的配置项
#    - 特别注意修改 OPENAI_CREDENTIALS 中的API密钥
#
# 8. 数据持久化：
#    - PostgreSQL数据: postgres_data volume
#    - Redis数据: redis_data volume
#    - Consul数据: consul_data volume
#
# 9. 网络配置：
#    - 所有服务在 microservices 网络中
#    - 服务间通过服务名通信
#    - 只有网关暴露80端口到宿主机
#
# 10. 健康检查：
#     - 所有服务都配置了健康检查
#     - 依赖服务健康后才启动应用服务
#     - 可通过 docker-compose ps 查看健康状态
